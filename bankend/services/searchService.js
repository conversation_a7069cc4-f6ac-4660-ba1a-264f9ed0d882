const { executeWithLog } = require('../db');
const EmbeddingService = require('./embeddingService');

class SearchService {
    constructor() {
        this.embeddingService = new EmbeddingService();
    }

    /**
     * 语义搜索笔记
     * @param {number} userId - 用户ID
     * @param {string} query - 搜索查询
     * @param {Object} options - 搜索选项
     * @returns {Promise<Array>} 搜索结果
     */
    async semanticSearch(userId, query, options = {}) {
        try {
            const {
                limit = 20,
                threshold = 0.7,
                includeContent = false,
                folderId = null
            } = options;

            console.log(`Semantic search for user ${userId}: "${query}"`);

            // 生成查询向量
            const queryVector = await this.embeddingService.generateEmbedding(query);

            // 获取用户的所有笔记向量
            let vectorQuery = `
                SELECT 
                    nv.note_id,
                    nv.embedding_vector,
                    nv.chunk_text,
                    nv.chunk_index,
                    n.title,
                    n.folder_id,
                    n.created_at,
                    n.updated_at
                FROM note_vectors nv
                JOIN notes n ON nv.note_id = n.id
                WHERE nv.user_id = ?
            `;
            
            const queryParams = [userId];

            // 如果指定了文件夹，添加文件夹过滤
            if (folderId !== null) {
                vectorQuery += ' AND n.folder_id = ?';
                queryParams.push(folderId);
            }

            vectorQuery += ' ORDER BY nv.note_id, nv.chunk_index';

            const [vectors] = await executeWithLog(vectorQuery, queryParams);

            if (vectors.length === 0) {
                return {
                    results: [],
                    totalCount: 0,
                    query,
                    searchTime: 0
                };
            }

            const startTime = Date.now();

            // 计算相似度
            const similarities = [];
            
            for (const vector of vectors) {
                try {
                    const embedding = JSON.parse(vector.embedding_vector);
                    const similarity = this.embeddingService.calculateCosineSimilarity(
                        queryVector, 
                        embedding
                    );

                    if (similarity >= threshold) {
                        similarities.push({
                            noteId: vector.note_id,
                            title: vector.title,
                            folderId: vector.folder_id,
                            chunkIndex: vector.chunk_index,
                            chunkText: vector.chunk_text,
                            similarity: similarity,
                            createdAt: vector.created_at,
                            updatedAt: vector.updated_at
                        });
                    }
                } catch (error) {
                    console.error(`Error processing vector for note ${vector.note_id}:`, error);
                }
            }

            // 按相似度排序
            similarities.sort((a, b) => b.similarity - a.similarity);

            // 按笔记分组，取每个笔记的最高相似度块
            const noteGroups = new Map();
            
            for (const item of similarities) {
                if (!noteGroups.has(item.noteId) || 
                    noteGroups.get(item.noteId).similarity < item.similarity) {
                    noteGroups.set(item.noteId, item);
                }
            }

            // 转换为数组并限制结果数量
            let results = Array.from(noteGroups.values())
                .slice(0, limit);

            // 如果需要包含完整内容，获取笔记内容
            if (includeContent) {
                const noteIds = results.map(r => r.noteId);
                if (noteIds.length > 0) {
                    const [notes] = await executeWithLog(
                        `SELECT id, content FROM notes WHERE id IN (${noteIds.map(() => '?').join(',')})`,
                        noteIds
                    );

                    const contentMap = new Map(notes.map(n => [n.id, n.content]));
                    results = results.map(r => ({
                        ...r,
                        content: contentMap.get(r.noteId) || ''
                    }));
                }
            }

            const searchTime = Date.now() - startTime;

            // 记录搜索历史
            await this.recordSearchHistory(userId, query, queryVector, results, {
                searchType: 'semantic',
                threshold,
                limit
            });

            console.log(`Semantic search completed in ${searchTime}ms, found ${results.length} results`);

            return {
                results: results.map(r => ({
                    noteId: r.noteId,
                    title: r.title,
                    folderId: r.folderId,
                    similarity: Math.round(r.similarity * 1000) / 1000, // 保留3位小数
                    matchedText: r.chunkText.substring(0, 200) + '...',
                    createdAt: r.createdAt,
                    updatedAt: r.updatedAt,
                    ...(includeContent && { content: r.content })
                })),
                totalCount: results.length,
                query,
                searchTime,
                threshold
            };

        } catch (error) {
            console.error('Error in semantic search:', error);
            throw new Error(`Semantic search failed: ${error.message}`);
        }
    }

    /**
     * 关键词搜索（传统搜索）
     * @param {number} userId - 用户ID
     * @param {string} query - 搜索查询
     * @param {Object} options - 搜索选项
     * @returns {Promise<Array>} 搜索结果
     */
    async keywordSearch(userId, query, options = {}) {
        try {
            const {
                limit = 20,
                includeContent = false,
                folderId = null
            } = options;

            console.log(`Keyword search for user ${userId}: "${query}"`);

            const startTime = Date.now();

            // 构建SQL查询
            let searchQuery = `
                SELECT 
                    n.id as noteId,
                    n.title,
                    n.folder_id as folderId,
                    n.created_at as createdAt,
                    n.updated_at as updatedAt
                    ${includeContent ? ', n.content' : ''}
                FROM notes n
                WHERE n.user_id = ?
                AND (n.title LIKE ? OR n.content LIKE ?)
            `;

            const queryParams = [userId, `%${query}%`, `%${query}%`];

            // 如果指定了文件夹，添加文件夹过滤
            if (folderId !== null) {
                searchQuery += ' AND n.folder_id = ?';
                queryParams.push(folderId);
            }

            searchQuery += ' ORDER BY n.updated_at DESC LIMIT ?';
            queryParams.push(limit);

            const [results] = await executeWithLog(searchQuery, queryParams);

            const searchTime = Date.now() - startTime;

            // 记录搜索历史
            await this.recordSearchHistory(userId, query, null, results, {
                searchType: 'keyword',
                limit
            });

            console.log(`Keyword search completed in ${searchTime}ms, found ${results.length} results`);

            return {
                results: results.map(r => ({
                    noteId: r.noteId,
                    title: r.title,
                    folderId: r.folderId,
                    createdAt: r.createdAt,
                    updatedAt: r.updatedAt,
                    ...(includeContent && { content: r.content })
                })),
                totalCount: results.length,
                query,
                searchTime,
                searchType: 'keyword'
            };

        } catch (error) {
            console.error('Error in keyword search:', error);
            throw new Error(`Keyword search failed: ${error.message}`);
        }
    }

    /**
     * 混合搜索（语义搜索 + 关键词搜索）
     * @param {number} userId - 用户ID
     * @param {string} query - 搜索查询
     * @param {Object} options - 搜索选项
     * @returns {Promise<Object>} 搜索结果
     */
    async hybridSearch(userId, query, options = {}) {
        try {
            const { limit = 20, threshold = 0.6 } = options;

            console.log(`Hybrid search for user ${userId}: "${query}"`);

            const startTime = Date.now();

            // 并行执行语义搜索和关键词搜索
            const [semanticResults, keywordResults] = await Promise.all([
                this.semanticSearch(userId, query, { 
                    ...options, 
                    limit: Math.ceil(limit * 0.7), 
                    threshold 
                }),
                this.keywordSearch(userId, query, { 
                    ...options, 
                    limit: Math.ceil(limit * 0.5) 
                })
            ]);

            // 合并结果，去重并按相关性排序
            const combinedResults = new Map();

            // 添加语义搜索结果（权重更高）
            semanticResults.results.forEach(result => {
                combinedResults.set(result.noteId, {
                    ...result,
                    relevanceScore: result.similarity * 0.7 + 0.3, // 语义搜索基础分数
                    searchType: 'semantic'
                });
            });

            // 添加关键词搜索结果
            keywordResults.results.forEach(result => {
                const existing = combinedResults.get(result.noteId);
                if (existing) {
                    // 如果已存在，提高相关性分数
                    existing.relevanceScore = Math.min(1.0, existing.relevanceScore + 0.2);
                    existing.searchType = 'hybrid';
                } else {
                    // 新结果，给予基础分数
                    combinedResults.set(result.noteId, {
                        ...result,
                        relevanceScore: 0.5, // 关键词搜索基础分数
                        searchType: 'keyword'
                    });
                }
            });

            // 转换为数组并排序
            const finalResults = Array.from(combinedResults.values())
                .sort((a, b) => b.relevanceScore - a.relevanceScore)
                .slice(0, limit);

            const searchTime = Date.now() - startTime;

            // 记录搜索历史
            await this.recordSearchHistory(userId, query, null, finalResults, {
                searchType: 'hybrid',
                threshold,
                limit
            });

            console.log(`Hybrid search completed in ${searchTime}ms, found ${finalResults.length} results`);

            return {
                results: finalResults.map(r => ({
                    ...r,
                    relevanceScore: Math.round(r.relevanceScore * 1000) / 1000
                })),
                totalCount: finalResults.length,
                query,
                searchTime,
                searchType: 'hybrid',
                breakdown: {
                    semantic: semanticResults.results.length,
                    keyword: keywordResults.results.length,
                    combined: finalResults.length
                }
            };

        } catch (error) {
            console.error('Error in hybrid search:', error);
            throw new Error(`Hybrid search failed: ${error.message}`);
        }
    }

    /**
     * 记录搜索历史
     * @param {number} userId - 用户ID
     * @param {string} query - 搜索查询
     * @param {Array} queryVector - 查询向量
     * @param {Array} results - 搜索结果
     * @param {Object} metadata - 搜索元数据
     */
    async recordSearchHistory(userId, query, queryVector, results, metadata) {
        try {
            const resultNoteIds = results.map(r => r.noteId || r.id);

            await executeWithLog(
                `INSERT INTO search_history 
                 (user_id, query_text, query_vector, result_count, result_note_ids, search_type, similarity_threshold)
                 VALUES (?, ?, ?, ?, ?, ?, ?)`,
                [
                    userId,
                    query,
                    queryVector ? JSON.stringify(queryVector) : null,
                    results.length,
                    JSON.stringify(resultNoteIds),
                    metadata.searchType || 'semantic',
                    metadata.threshold || null
                ]
            );
        } catch (error) {
            console.error('Error recording search history:', error);
            // 不抛出错误，避免影响搜索功能
        }
    }

    /**
     * 获取搜索历史
     * @param {number} userId - 用户ID
     * @param {number} limit - 限制数量
     * @returns {Promise<Array>} 搜索历史
     */
    async getSearchHistory(userId, limit = 10) {
        try {
            const [history] = await executeWithLog(
                `SELECT query_text, search_type, result_count, created_at
                 FROM search_history 
                 WHERE user_id = ? 
                 ORDER BY created_at DESC 
                 LIMIT ?`,
                [userId, limit]
            );

            return history;
        } catch (error) {
            console.error('Error getting search history:', error);
            return [];
        }
    }
}

module.exports = SearchService;
