const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { apiLimiter } = require('../middleware/rateLimiter');
const EmbeddingService = require('../services/embeddingService');
const SearchService = require('../services/searchService');
const AnalysisService = require('../services/analysisService');
const OrganizationService = require('../services/organizationService');

// 初始化服务
const embeddingService = new EmbeddingService();
const searchService = new SearchService();
const analysisService = new AnalysisService();
const organizationService = new OrganizationService();

// 语义搜索接口
router.post('/search', apiLimiter, authenticateToken, async (req, res) => {
    try {
        const { query, searchType = 'semantic', options = {} } = req.body;
        const userId = req.user.userId;

        if (!query || typeof query !== 'string' || query.trim().length === 0) {
            return res.status(400).json({ message: '搜索查询不能为空' });
        }

        if (query.length > 500) {
            return res.status(400).json({ message: '搜索查询过长，请限制在500字符以内' });
        }

        console.log(`[KNOWLEDGE] User ${req.user.username} searching: "${query}" (${searchType})`);

        let results;
        switch (searchType) {
            case 'semantic':
                results = await searchService.semanticSearch(userId, query, options);
                break;
            case 'keyword':
                results = await searchService.keywordSearch(userId, query, options);
                break;
            case 'hybrid':
                results = await searchService.hybridSearch(userId, query, options);
                break;
            default:
                return res.status(400).json({ message: '不支持的搜索类型' });
        }

        res.json({
            success: true,
            data: results
        });

    } catch (error) {
        console.error('Knowledge search error:', error);
        res.status(500).json({ 
            success: false,
            message: '搜索失败，请稍后重试',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// 分析笔记内容
router.post('/analyze/:noteId', apiLimiter, authenticateToken, async (req, res) => {
    try {
        const noteId = parseInt(req.params.noteId);
        const userId = req.user.userId;

        if (isNaN(noteId)) {
            return res.status(400).json({ message: '无效的笔记ID' });
        }

        // 获取笔记信息
        const { executeWithLog } = require('../db');
        const [notes] = await executeWithLog(
            'SELECT id, title, content FROM notes WHERE id = ? AND user_id = ?',
            [noteId, userId]
        );

        if (notes.length === 0) {
            return res.status(404).json({ message: '笔记不存在或无权访问' });
        }

        const note = notes[0];
        console.log(`[KNOWLEDGE] Analyzing note ${noteId} for user ${req.user.username}`);

        // 执行分析
        const analysis = await analysisService.analyzeNoteContent(
            noteId, 
            userId, 
            note.title, 
            note.content
        );

        res.json({
            success: true,
            data: analysis
        });

    } catch (error) {
        console.error('Note analysis error:', error);
        res.status(500).json({ 
            success: false,
            message: '分析失败，请稍后重试',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// 获取笔记分析结果
router.get('/analysis/:noteId', authenticateToken, async (req, res) => {
    try {
        const noteId = parseInt(req.params.noteId);
        const userId = req.user.userId;

        if (isNaN(noteId)) {
            return res.status(400).json({ message: '无效的笔记ID' });
        }

        const analysis = await analysisService.getNoteAnalysis(noteId, userId);

        if (!analysis) {
            return res.status(404).json({ message: '未找到分析结果' });
        }

        res.json({
            success: true,
            data: analysis
        });

    } catch (error) {
        console.error('Get analysis error:', error);
        res.status(500).json({ 
            success: false,
            message: '获取分析结果失败',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// 向量化笔记内容
router.post('/vectorize/:noteId', apiLimiter, authenticateToken, async (req, res) => {
    try {
        const noteId = parseInt(req.params.noteId);
        const userId = req.user.userId;

        if (isNaN(noteId)) {
            return res.status(400).json({ message: '无效的笔记ID' });
        }

        // 获取笔记内容
        const { executeWithLog } = require('../db');
        const [notes] = await executeWithLog(
            'SELECT id, content FROM notes WHERE id = ? AND user_id = ?',
            [noteId, userId]
        );

        if (notes.length === 0) {
            return res.status(404).json({ message: '笔记不存在或无权访问' });
        }

        const note = notes[0];
        console.log(`[KNOWLEDGE] Vectorizing note ${noteId} for user ${req.user.username}`);

        // 执行向量化
        const result = await embeddingService.processNoteContent(
            noteId, 
            userId, 
            note.content
        );

        res.json({
            success: true,
            data: result
        });

    } catch (error) {
        console.error('Note vectorization error:', error);
        res.status(500).json({ 
            success: false,
            message: '向量化失败，请稍后重试',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// 批量处理用户笔记
router.post('/batch/process', apiLimiter, authenticateToken, async (req, res) => {
    try {
        const { operations = ['vectorize', 'analyze'] } = req.body;
        const userId = req.user.userId;

        console.log(`[KNOWLEDGE] Batch processing for user ${req.user.username}: ${operations.join(', ')}`);

        const results = {};

        // 向量化处理
        if (operations.includes('vectorize')) {
            try {
                results.vectorization = await embeddingService.processAllUserNotes(userId);
            } catch (error) {
                console.error('Batch vectorization error:', error);
                results.vectorization = { error: error.message };
            }
        }

        // 分析处理
        if (operations.includes('analyze')) {
            try {
                results.analysis = await analysisService.analyzeAllUserNotes(userId);
            } catch (error) {
                console.error('Batch analysis error:', error);
                results.analysis = { error: error.message };
            }
        }

        // 关系发现
        if (operations.includes('relations')) {
            try {
                results.relations = await organizationService.discoverNoteRelations(userId);
            } catch (error) {
                console.error('Batch relations error:', error);
                results.relations = { error: error.message };
            }
        }

        // 标签管理
        if (operations.includes('tags')) {
            try {
                results.tags = await organizationService.autoManageTags(userId);
            } catch (error) {
                console.error('Batch tags error:', error);
                results.tags = { error: error.message };
            }
        }

        res.json({
            success: true,
            data: results
        });

    } catch (error) {
        console.error('Batch processing error:', error);
        res.status(500).json({ 
            success: false,
            message: '批量处理失败，请稍后重试',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// 获取笔记关联信息
router.get('/relations/:noteId', authenticateToken, async (req, res) => {
    try {
        const noteId = parseInt(req.params.noteId);
        const userId = req.user.userId;

        if (isNaN(noteId)) {
            return res.status(400).json({ message: '无效的笔记ID' });
        }

        const relations = await organizationService.getNoteRelations(userId, noteId);

        res.json({
            success: true,
            data: relations
        });

    } catch (error) {
        console.error('Get relations error:', error);
        res.status(500).json({ 
            success: false,
            message: '获取关联信息失败',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// 查找相似笔记
router.get('/similar/:noteId', authenticateToken, async (req, res) => {
    try {
        const noteId = parseInt(req.params.noteId);
        const userId = req.user.userId;
        const { threshold = 0.7, limit = 10 } = req.query;

        if (isNaN(noteId)) {
            return res.status(400).json({ message: '无效的笔记ID' });
        }

        const similarNotes = await organizationService.findSimilarNotes(userId, noteId, {
            threshold: parseFloat(threshold),
            limit: parseInt(limit),
            excludeSelf: true
        });

        res.json({
            success: true,
            data: similarNotes
        });

    } catch (error) {
        console.error('Find similar notes error:', error);
        res.status(500).json({ 
            success: false,
            message: '查找相似笔记失败',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// 获取文件夹推荐
router.get('/recommendations/folders', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;

        console.log(`[KNOWLEDGE] Generating folder recommendations for user ${req.user.username}`);

        const recommendations = await organizationService.recommendFolderStructure(userId);

        res.json({
            success: true,
            data: recommendations
        });

    } catch (error) {
        console.error('Folder recommendations error:', error);
        res.status(500).json({ 
            success: false,
            message: '获取文件夹推荐失败',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// 获取用户分析统计
router.get('/stats', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;

        const stats = await analysisService.getUserAnalysisStats(userId);

        res.json({
            success: true,
            data: stats
        });

    } catch (error) {
        console.error('Get stats error:', error);
        res.status(500).json({ 
            success: false,
            message: '获取统计信息失败',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// 获取搜索历史
router.get('/search/history', authenticateToken, async (req, res) => {
    try {
        const userId = req.user.userId;
        const { limit = 10 } = req.query;

        const history = await searchService.getSearchHistory(userId, parseInt(limit));

        res.json({
            success: true,
            data: history
        });

    } catch (error) {
        console.error('Get search history error:', error);
        res.status(500).json({ 
            success: false,
            message: '获取搜索历史失败',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

module.exports = router;
